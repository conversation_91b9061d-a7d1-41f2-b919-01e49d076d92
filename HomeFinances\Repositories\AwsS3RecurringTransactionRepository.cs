using System.Text;
using System.Text.Json;
using Amazon.S3;
using Amazon.S3.Model;
using HomeFinances.Models;


namespace HomeFinances.Repositories
{
    /// <summary>
    /// AWS S3-based implementation of IRecurringTransactionRepository using JSON files stored in S3.
    /// </summary>
    public class AwsS3RecurringTransactionRepository : IRecurringTransactionRepository
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<AwsS3RecurringTransactionRepository> _logger;
        private readonly string _bucketName;
        private const string S3KeyPrefix = "recurring-transactions";
        private const string S3FileName = "recurring-transactions.json";

        public AwsS3RecurringTransactionRepository(IAmazonS3 s3Client, ILogger<AwsS3RecurringTransactionRepository> logger)
        {
            _s3Client = s3Client ?? throw new ArgumentNullException(nameof(s3Client), "S3 client cannot be null. AWS backend is required.");
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Get bucket name from environment variable
            var connectionJson = Environment.GetEnvironmentVariable("AWS_S3_HOMEFINANCE_CONNECTION");
            if (!string.IsNullOrEmpty(connectionJson))
            {
                try
                {
                    var connectionSettings = JsonSerializer.Deserialize<Dictionary<string, string>>(connectionJson);
                    if (connectionSettings != null && connectionSettings.TryGetValue("BucketName", out var bucketName))
                    {
                        _bucketName = bucketName;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to parse AWS_S3_HOMEFINANCE_CONNECTION JSON");
                    throw new InvalidOperationException("Failed to parse AWS connection settings", ex);
                }
            }

            if (string.IsNullOrEmpty(_bucketName))
            {
                _bucketName = "home-finances";
                _logger.LogWarning("Using default bucket name: {BucketName}", _bucketName);
            }

            _logger.LogInformation("Initialized AwsS3RecurringTransactionRepository with bucket: {BucketName}", _bucketName);
        }

        /// <summary>
        /// Verifies that the S3 bucket exists and is accessible
        /// </summary>
        private async Task<bool> VerifyBucketExistsAsync()
        {
            try
            {
                var response = await _s3Client.ListBucketsAsync().ConfigureAwait(false);
                var bucketExists = response.Buckets.Any(b => b.BucketName == _bucketName);

                if (!bucketExists)
                {
                    _logger.LogWarning("S3 bucket {BucketName} does not exist", _bucketName);
                    throw new InvalidOperationException($"S3 bucket {_bucketName} does not exist. Please create it before using this repository.");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying S3 bucket {BucketName}", _bucketName);
                throw new InvalidOperationException($"Error accessing S3 bucket {_bucketName}. AWS backend is required.", ex);
            }
        }

        /// <summary>
        /// Gets the S3 key for the recurring transactions file
        /// </summary>
        private string GetS3Key()
        {
            return $"{S3KeyPrefix}/{S3FileName}";
        }

        /// <inheritdoc />
        public async Task<IEnumerable<RecurringTransaction>> GetAllAsync()
        {
            try
            {
                await VerifyBucketExistsAsync().ConfigureAwait(false);

                var s3Key = GetS3Key();

                try
                {
                    var request = new GetObjectRequest
                    {
                        BucketName = _bucketName,
                        Key = s3Key
                    };

                    var response = await _s3Client.GetObjectAsync(request).ConfigureAwait(false);

                    using var reader = new StreamReader(response.ResponseStream);
                    var jsonContent = await reader.ReadToEndAsync().ConfigureAwait(false);

                    if (string.IsNullOrWhiteSpace(jsonContent))
                    {
                        _logger.LogInformation("Empty recurring transactions file found in S3");
                        return new List<RecurringTransaction>();
                    }

                    var transactions = JsonSerializer.Deserialize<List<RecurringTransaction>>(jsonContent);

                    _logger.LogInformation("Successfully loaded {Count} recurring transactions from S3 bucket {BucketName}, key {Key}",
                        transactions?.Count ?? 0, _bucketName, s3Key);

                    return transactions ?? new List<RecurringTransaction>();
                }
                catch (AmazonS3Exception s3Ex) when (s3Ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("Recurring transactions file not found in S3 bucket {BucketName}, key {Key}. Returning empty list.",
                        _bucketName, s3Key);

                    return new List<RecurringTransaction>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading recurring transactions from S3");
                throw new InvalidOperationException("Failed to read recurring transactions from AWS S3. AWS backend is required.", ex);
            }
        }

        /// <inheritdoc />
        public async Task<RecurringTransaction> GetByIdAsync(Guid id)
        {
            var allTransactions = await GetAllAsync().ConfigureAwait(false);
            var transaction = allTransactions.FirstOrDefault(t => t.Id == id);
            
            if (transaction == null)
            {
                _logger.LogWarning("Recurring transaction with ID {TransactionId} not found", id);
                throw new InvalidOperationException($"Recurring transaction with ID {id} not found");
            }

            return transaction;
        }

        /// <inheritdoc />
        public async Task AddOrUpdateAsync(RecurringTransaction transaction)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction));

            try
            {
                var allTransactions = (await GetAllAsync().ConfigureAwait(false)).ToList();
                
                // Remove existing transaction with same ID if it exists
                var existingIndex = allTransactions.FindIndex(t => t.Id == transaction.Id);
                if (existingIndex >= 0)
                {
                    allTransactions[existingIndex] = transaction;
                    _logger.LogInformation("Updated existing recurring transaction with ID {TransactionId}", transaction.Id);
                }
                else
                {
                    allTransactions.Add(transaction);
                    _logger.LogInformation("Added new recurring transaction with ID {TransactionId}", transaction.Id);
                }

                await WriteAllAsync(allTransactions).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding/updating recurring transaction with ID {TransactionId}", transaction.Id);
                throw new InvalidOperationException($"Failed to add/update recurring transaction with ID {transaction.Id}. AWS backend is required.", ex);
            }
        }

        /// <inheritdoc />
        public async Task DeleteAsync(Guid id)
        {
            try
            {
                var allTransactions = (await GetAllAsync().ConfigureAwait(false)).ToList();
                
                var existingIndex = allTransactions.FindIndex(t => t.Id == id);
                if (existingIndex >= 0)
                {
                    allTransactions.RemoveAt(existingIndex);
                    await WriteAllAsync(allTransactions).ConfigureAwait(false);
                    _logger.LogInformation("Deleted recurring transaction with ID {TransactionId}", id);
                }
                else
                {
                    _logger.LogWarning("Attempted to delete non-existent recurring transaction with ID {TransactionId}", id);
                    throw new InvalidOperationException($"Recurring transaction with ID {id} not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting recurring transaction with ID {TransactionId}", id);
                throw new InvalidOperationException($"Failed to delete recurring transaction with ID {id}. AWS backend is required.", ex);
            }
        }

        /// <summary>
        /// Writes all recurring transactions to S3
        /// </summary>
        private async Task WriteAllAsync(IEnumerable<RecurringTransaction> transactions)
        {
            try
            {
                await VerifyBucketExistsAsync().ConfigureAwait(false);

                var s3Key = GetS3Key();
                var jsonOptions = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var jsonContent = JsonSerializer.Serialize(transactions, jsonOptions);

                using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(jsonContent));

                var putRequest = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = s3Key,
                    InputStream = memoryStream,
                    ContentType = "application/json"
                };

                await _s3Client.PutObjectAsync(putRequest).ConfigureAwait(false);

                _logger.LogInformation("Successfully wrote {Count} recurring transactions to S3 bucket {BucketName}, key {Key}",
                    transactions.Count(), _bucketName, s3Key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing recurring transactions to S3");
                throw new InvalidOperationException("Failed to write recurring transactions to AWS S3. AWS backend is required.", ex);
            }
        }
    }
}
